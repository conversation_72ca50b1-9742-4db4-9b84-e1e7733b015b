'use client';

import { CreateOrganization } from '@repo/auth/components/create-organization';

export function CreateOrganizationWrapper() {
  return (
    <CreateOrganization
      onSuccess={() => {
        // Force a hard refresh to ensure session is updated with new organization
        // This ensures the activeOrganizationId is properly set in the session
        window.location.href = '/onboarding';
      }}
      onError={(error) => {
        // Error handling is done in the component
        console.error('Failed to create organization:', error);
      }}
    />
  );
}
