import { OnboardingSidebar } from '@repo/auth/components/onboarding/onboarding-sidebar';
import { Button } from '@repo/design-system/components/ui/button';
import {
  SidebarInset,
  SidebarProvider,
} from '@repo/design-system/components/ui/sidebar';
import { createMetadata } from '@repo/seo/metadata';
import type { Metadata } from 'next';
import type * as React from 'react';
import { OnboardingFormClient } from '../components/onboarding-form-client';

const title = 'Complete your onboarding';
const description = 'Set up your business information to get started.';

export const metadata: Metadata = createMetadata({ title, description });

const OnboardingPage = () => {
  return (
    <SidebarProvider
      style={
        {
          '--sidebar-width': '16rem',
        } as React.CSSProperties
      }
    >
      <OnboardingSidebar />

      <SidebarInset>
        <div className="flex h-full w-full justify-center">
          <div className="w-full max-w-4xl space-y-6 px-8 pt-12 md:p-8">
            <OnboardingFormClient />
          </div>
        </div>

        {/* Force a hard refresh to ensure session is updated with organization */}
        <Button
          variant="ghost"
          className="fixed top-4 right-4"
          onClick={() => {
            window.location.href = '/';
          }}
        >
          Skip for now
        </Button>
      </SidebarInset>
    </SidebarProvider>
  );
};

export default OnboardingPage;
